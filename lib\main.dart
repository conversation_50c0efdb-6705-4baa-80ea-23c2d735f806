import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'services/blufi_service.dart';
import 'services/blufi_service_copy.dart';
import 'views/device_scan_page.dart';
import 'views/blufi_test_page.dart';
import 'utils/constants.dart';

void main() {
  runApp(const ESP32BluFiApp());
}

class ESP32BluFiApp extends StatelessWidget {
  const ESP32BluFiApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        // 提供BluFi服务单例 - 使用单例模式
        // Provider<BluFiService>(
        //   create: (_) => BluFiService(),
        //   lazy: false, // 立即创建，确保服务可用
        // ),
        Provider(
          create: (context) => BlufiService(),
        ),
      ],
      child: MaterialApp(
        title: AppConstants.appName,
        theme: ThemeData(
          colorScheme: ColorScheme.fromSeed(
            seedColor: const Color(AppColors.primaryColorValue),
          ),
          useMaterial3: true,
          appBarTheme: const AppBarTheme(
            centerTitle: true,
            elevation: 2,
          ),
          cardTheme: CardTheme(
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppConstants.borderRadius),
            ),
          ),
          elevatedButtonTheme: ElevatedButtonThemeData(
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(
                horizontal: AppConstants.largePadding,
                vertical: AppConstants.defaultPadding,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppConstants.borderRadius),
              ),
            ),
          ),
          outlinedButtonTheme: OutlinedButtonThemeData(
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(
                horizontal: AppConstants.largePadding,
                vertical: AppConstants.defaultPadding,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppConstants.borderRadius),
              ),
            ),
          ),
        ),
        // home: const DeviceScanPageProvider(),
        home: const BlufiTestPage(),
        debugShowCheckedModeBanner: false,
      ),
    );
  }
}
